# Convert a telescan file (.tlscan) to a configspace file (.coe) 


Before we begin, I want to thank [k<PERSON><PERSON><PERSON>](https://github.com/kweather<PERSON>) for sharing this Python script he made which will be used to convert a telescan file to the config-space file.

Edit. I did have to change a line for it to work.

#### Download the telescan_to_coe.py file above


1. Open CMD and cd to the location of the downloaded python file

You will need to be in the directory of the python file, and you will need to change the directory of where the telescan file is located. 

In my instance, my .tlscan file is on my desktop and is called Ax200.

2. In CMD type in `python telescan_to_coe.py C:/Users/<USER>/Desktop/Ax200.tlscan`

Once you let the script run, the generated file (output.coe) should be on your Desktop. Unless you change the output directory and the file name on line 22.

Replace the contents of the `configspace.coe` file in `ip/pcileech_cfgspace.coe` with the content in the `output.coe` file generated by the python script.

