# 1:1 CFG space of real PCIe device

### Requirements
1. Telescan

For this guide, I will be using telescan to get the device config

Run, [.tlscan to .coe](https://github.com/Rakeshmonkee/DMA/tree/main/.tlscan%20to%20.coe) python file and generate the output.coe file

This part below needs to be edited before generating the ipcore within vivado

1. With the generated file, replace the contents of `pcileech_cfgspace.coe` with the content of `output.coe`
   
2. in `pcileech_fifo.sv` change the value of `CFGTLP ZERO DATA` to `0`

3. in `pcileech_pcie_cfg_a7.sv` change `DSN`, `Master Abort Flag`, `Bus Master Flag`

After Changing these, you can now generate the IP core

The next steps are done within Vivado

1. Match everything seen in the header that you see in telescan

2. Change 

```
EXT_CFG_CAP_PTR 
EXT_CFG_XP_CAP_PTR
```

in the core_top.sv file to the blocks, you want your capability to start

3. generate bitstream and flash
