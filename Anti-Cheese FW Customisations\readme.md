IDs:
- Vendor ID 
- Device ID 
- Revision ID 
- Subsystem Vendor ID 
- Subsystem ID 


Class Code:
- Base Class Menu 
- Base Class Value
- Sub Class Interface Menu
- Sub Class Value
- Cardbus CIS Pointer


Bars (0):
- Type
- Size Unit
- Size Value
- Prefetchable


Device Capabilities Register:
- Max Payload Size
- Extended Tag Field
- Extended Tag Default
- Phantom Functions
- Acceptable L0s Latency
- Acceptable L1 Latency


Link Status Register:
- Slot Clock Configuration


Legacy Interrupt Settings:
- Interrupt PIN


MSI Capabilities:
- MSI Capability Structure
- 64 bit Address Structure


MSIx Capabilities:
-- will need to calculate table and PBA Offset


Power Management Registers:
- Device Specific Initialization
- D1 Support
 -D2 Support


PME SUPPORT:
- D0
- D1
- D2
- D3hot
- D3cold
- No Soft Reset

DSN Capability:
- DSN Capability
- Change DSN value within `pcileech_pcie_cfg_a7.sv`


Vendor Specific Capability:
- VSEC Capability


Virtual Channel Capability:
- VC Capability
- Reject Snoop Transactions
